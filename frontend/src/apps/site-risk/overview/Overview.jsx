import PropTypes from 'prop-types';
import { useContext, useState } from 'react';
import { useTranslation } from 'react-i18next';
import Button from '../../../common/components/Button';
import { SiteRiskContext } from '../../../common/context/SiteRiskContext';
import { openWithLanguage } from '../../../common/utils/navigationUtils';
import SiteRiskPeriodicCheckup from '../SiteRiskPeriodicCheckup';
import OverviewCard from './OverviewCard';
import OverviewGrid from './OverviewGrid';

const NdsIcon = () => (
  <span className="icon-base icon-sec-security icon-size20 icon-color-darkGreen" />
);
const CloudIcon = () => (
  <span className="icon-base icon-sec-cloud icon-size20 icon-color-darkGreen" />
);
const SslIcon = () => (
  <span className="icon-base icon-sec-ssl icon-size20 icon-color-darkGreen" />
);
const ImpersonationIcon = () => (
  <span className="icon-base icon-sec-impersonation icon-size20 icon-color-darkGreen" />
);

function Overview({ fqdn, nds, cloud, ssl, impersonation, code, result }) {
  const { t } = useTranslation('check_site-risk');
  const [currentSectionIdx, setCurrentSectionIdx] = useState(0);

  const NDS_RANKS = {
    A: t('安全です'),
    B: t('安全です'),
    C: t('要対策'),
    D: t('要対策'),
    E: t('要緊急対応'),
  };

  const IMPERSONATION_RANKS = {
    A: t('安全です'),
    B: t('要注意'),
    C: t('危険'),
    D: t('非常に危険'),
  };

  const STATUS_NOTES = {
    alert: t('早急な対応が必要な項目があります'),
    warning: t('概ね安全ですが、引き続き注意を要する状態です'),
    safe: t('すべての診断項目が安全な状態です'),
  };

  const { worstStatus } = useContext(SiteRiskContext);

  const sections = [
    {
      id: 'anchorLinker_nds',
      targetId: 'anchorLink_nds',
      title: t('Webサイト脆弱性診断'),
      icon: NdsIcon,
      status: nds.status,
      rank: nds.rank,
      type: 'nds',
      text: NDS_RANKS[nds.rank],
    },
    {
      id: 'anchorLinker_cloud',
      targetId: 'anchorLink_cloud',
      title: t('クラウド利用・リスク診断'),
      icon: CloudIcon,
      status: cloud.status,
      type: 'cloud',
      text: cloud.text,
    },
    {
      id: 'anchorLinker_ssl',
      targetId: 'anchorLink_ssl',
      title: t('実在証明・盗聴防止（SSL）診断'),
      icon: SslIcon,
      status: ssl.status,
      type: 'ssl',
      text: ssl.text,
    },
    {
      id: 'anchorLinker_impersonation',
      targetId: 'anchorLink_impersonation',
      title: t('なりすまし診断'),
      icon: ImpersonationIcon,
      status: impersonation.status,
      rank: impersonation.rank,
      type: 'impersonation',
      text: IMPERSONATION_RANKS[impersonation.rank],
    },
  ];

  const handleNewDiagnostic = () => {
    openWithLanguage('/security/');
  };

  return (
    <div className="c-overview">
      {worstStatus === 'alert'
        ? (
          <div className="c-overview__note c-overview__note--error">
            <span className="icon-base icon-sec-caution icon-color-white" />
            {t('早急な対応が必要な項目があります')}
            {t(STATUS_NOTES[worstStatus])}
          </div>
        )
        : worstStatus === 'warning'
          ? (
            <div className="c-overview__note c-overview__note--warning">
              {t('概ね安全ですが、引き続き注意を要する状態です')}
            </div>
          )
          : (
            <div className="c-overview__note c-overview__note--safe">
              {t('すべての診断項目が安全な状態です')}
            </div>
          )}
      <div className="c-overview__head">
        <dl>
          <dt>{t('診断対象のURL')}</dt>
          <dd>{fqdn}</dd>
        </dl>
        <div className="c-overview__button">
          <Button
            id="another_url"
            onClick={handleNewDiagnostic}
            external
            exIcon="xsmall"
            variant="textUnderLine"
          >
            {t('他のURLを診断')}
          </Button>
        </div>
      </div>
      <div className="c-overview__check">
        <SiteRiskPeriodicCheckup
          code={code}
          nextCheckedAt={result.configuration?.nextCheckedAt}
          isRegularly={result.configuration?.isRegularly}
          interval={result.configuration?.interval}
          isNotification={result.configuration?.isNotification}
          createdAt={result.overview.createdAt}
        />
      </div>
      <div className="c-overview__nav">
        <OverviewGrid>
          {sections.map((section, idx) => (
            <OverviewCard
              key={section.id}
              {...section}
              className={`c-overviewCard c-overviewCard--tab c-tab__btn ${idx === currentSectionIdx ? 'c-tab__btn--active' : ''}`}
              onClick={() => {
                const element = document.getElementById(section.targetId);
                if (element) {
                  if (currentSectionIdx !== idx) {
                    const currentTarget = document.getElementById(
                      sections[currentSectionIdx].targetId,
                    );
                    currentTarget.classList.remove('c-tab__content--active');

                    element.classList.add('c-tab__content--active');
                    setCurrentSectionIdx(idx);
                  }
                }
              }}
            />
          ))}
        </OverviewGrid>
      </div>
    </div>
  );
}

Overview.propTypes = {
  fqdn: PropTypes.string.isRequired,
  nds: PropTypes.shape({
    status: PropTypes.string.isRequired,
    rank: PropTypes.string.isRequired,
  }).isRequired,
  cloud: PropTypes.shape({
    status: PropTypes.string.isRequired,
    text: PropTypes.string.isRequired,
  }).isRequired,
  ssl: PropTypes.shape({
    status: PropTypes.string.isRequired,
    text: PropTypes.string.isRequired,
  }).isRequired,
  impersonation: PropTypes.shape({
    status: PropTypes.string.isRequired,
    rank: PropTypes.string.isRequired,
  }).isRequired,
  code: PropTypes.string.isRequired,
  result: PropTypes.shape({
    configuration: PropTypes.shape({
      nextCheckedAt: PropTypes.string,
      isRegularly: PropTypes.bool,
      interval: PropTypes.number,
      isNotification: PropTypes.bool,
    }),
    overview: PropTypes.shape({ createdAt: PropTypes.string }).isRequired,
  }).isRequired,
};

export default Overview;
