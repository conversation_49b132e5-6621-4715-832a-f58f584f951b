import { useEffect, useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { useMediaQuery } from 'react-responsive';
import Button from '../../common/components/Button';
import VerticalSlider from '../../common/components/VerticalSlider';
import useChatHandler from '../../common/hooks/useChatHandler';

import {
  getFqdn,
  isValidEmail,
  isValidFqdn,
  isValidUrl,
} from '../../common/utils';
import { normalizeLanguage } from '../../common/utils/languageUtils';
import { appendLangQuery, navigateWithLanguage } from '../../common/utils/navigationUtils';
import Header from './Header';
import SearchInput from './SearchInput';
import Tabs from './Tabs';

function Home() {
  const { t, i18n } = useTranslation('home');
  const searchParams = new URLSearchParams(window.location.search);
  const tabParam = searchParams.get('tab');
  const defaultActiveTab = tabParam === 'chat' ? 'chat' : 'diagnostic';

  const [isLoading, setIsLoading] = useState(false);
  const [grecaptcha, setGrecaptcha] = useState(null);
  const [activeTab, setActiveTab] = useState(defaultActiveTab);
  const [diagnosticValue, setDiagnosticValue] = useState('');
  const [chatValue, setChatValue] = useState('');
  const [error, setError] = useState('');
  const { isChatValidAndAllowed, chatError } = useChatHandler();

  useEffect(() => {
    const { grecaptcha } = window;
    if (grecaptcha) {
      grecaptcha.ready(() => {
        setGrecaptcha(grecaptcha);
      });
    }
    const state = localStorage.getItem('state');
    if (state) {
      setDiagnosticValue(JSON.parse(state).diagnosticValue);
      localStorage.removeItem('state');
    }
  }, []);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (activeTab === 'diagnostic') {
      if (diagnosticValue.trim()) {
        handleDiagnosticSubmit(diagnosticValue.trim());
        return;
      }
    }
    if (chatValue.trim()) {
      handleChatSubmit(chatValue.trim());
    }
  };

  const handleDiagnosticSubmit = async (value) => {
    setIsLoading(true);

    try {
      if (!grecaptcha) {
        throw new Error('Not fount grecaptcha');
      }
      const recaptchaToken = await grecaptcha.execute(
        import.meta.env.VITE_RECAPTCHA_SITE_KEY,
        { action: 'submit' },
      );
      if (isValidEmail(value)) {
        const supportedLang = normalizeLanguage(i18n.language);

        const response = await fetch(
          `${import.meta.env.VITE_API_HOST}/api/email`,
          {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ email: value, recaptchaToken, lang: supportedLang }),
          },
        );

        if (response.ok) {
          localStorage.setItem('state', JSON.stringify({ email: value }));
          navigateWithLanguage('/security/check/complete/');
        } else {
          const { status, message } = await response.json();
          if (status === 'error') {
            throw new Error(`Email api error message: ${message}`);
          }
        }
      } else if (isValidFqdn(value) || isValidUrl(value)) {
        localStorage.setItem('state', JSON.stringify({ fqdn: getFqdn(value) }));
        navigateWithLanguage('/security/check/email/');
      } else {
        setError(t('無効なメールアドレスまたはURLです'));
      }
    } catch (err) {
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleChatSubmit = async (value) => {
    if (!isChatValidAndAllowed(value)) {
      return;
    }

    localStorage.setItem('state', JSON.stringify({ content: value }));
    navigateWithLanguage('/security/check/chat/');
  };

  return (
    <section className="p-home">
      <div className="p-home__inner">
        <div className="p-home__header">
          <Header title={t('総合ネットセキュリティサービス GMOセキュリティ24')} />
        </div>
        <p className="p-home__text">
          {activeTab === 'diagnostic'
            ? (
              <Trans
                i18nKey="パスワードの漏洩、WEBの侵入リスクなどを<span>無料でお調べいたします</span>"
                components={{ span: <span /> }}
              />
            )
            : (
              <>{t('ネット利用に関するご不安にお答えいたします')}</>
            )}
        </p>
        <div className="p-home__search">
          <form onSubmit={handleSubmit} id="securityTopForm">
            {activeTab === 'diagnostic'
              ? (
                <div
                  role="tabpanel"
                  id="tab-panel1"
                  className={
                    activeTab == 'diagnostic'
                      ? 'p-home__content p-home__content--active'
                      : 'p-home__content'
                  }
                  aria-labelledby="tab1"
                  tabIndex={activeTab == 'diagnostic' ? '0' : '-1'}
                >
                  <SearchInput
                    key="searchInput-diagnostic"
                    onChange={value => setDiagnosticValue(value)}
                    initValue={diagnosticValue}
                  />
                  {error && (
                    <p className="p-home__error">
                      {error}
                    </p>
                  )}
                </div>
              )
              : (
                <div
                  role="tabpanel"
                  id="tab-panel2"
                  className={
                    activeTab == 'chat'
                      ? 'p-home__content p-home__content--active'
                      : 'p-home__content'
                  }
                  aria-labelledby="tab2"
                  tabIndex={activeTab == 'chat' ? '0' : '-1'}
                >
                  <SearchInput
                    placeholder={t('セキュリティの懸念を入力')}
                    key="searchInput-chat"
                    onChange={value => setChatValue(value)}
                    initValue={chatValue}
                  />
                  {chatError && (
                    <p className="p-home__error">
                      <span className="icon-base icon-sec-caution icon-color-white" />
                      {chatError}
                    </p>
                  )}
                </div>
              )}
            <div className="p-home__tabs">
              <Tabs activeTab={activeTab} onTabChange={setActiveTab} />
              <div className="p-home__searchButton">
                {activeTab === 'diagnostic'
                  ? (
                    <Button type="submit" disabled={isLoading} variant="searchHome" form="securityTopForm">
                      {isLoading
                        ? (
                          <div className="c-loader c-loader--button" />
                        )
                        : (
                          <span className="icon-base icon-sec-search-bold icon-size16 icon-color-black" />
                        )}
                      <span className="p-home__searchButtonText">{t('無料診断')}</span>
                    </Button>
                  )
                  : (
                    <Button type="submit" disabled={isLoading} variant="searchHome" form="securityTopForm">
                      {isLoading
                        ? (
                          <div className="c-loader c-loader--button" />
                        )
                        : (
                          <span className="icon-base icon-sec-chatbubble-bold icon-size16 icon-color-black" />
                        )}
                      <span className="p-home__searchButtonText">{t('相談')}</span>
                    </Button>
                  )}
              </div>
            </div>
          </form>
        </div>
        <div className="p-home__linkBox">
          <a
            key="top_yourbrand_link"
            id="top_yourbrand_link"
            href={appendLangQuery('/security/yourbrand/')}
            target="_blank"
            rel="noopener"
            className="p-home__pageLink"
          >
            <span className="icon-base icon-size20 icon-sec-www p-home__pageLinkIcon" />
            <div>
              <Trans
                i18nKey="10年に1度のチャンス！<break>「<em>.貴社名</em>」でなりすまし対策</break>"
                components={{
                  break: <span className="p-home__textBreak" />,
                  em: <span className="p-home__textEm" />,
                }}
              />
            </div>
          </a>
          <a
            key="top_oss_link"
            id="top_oss_link"
            href={appendLangQuery('/security/oss-support/')}
            target="_blank"
            rel="noopener"
            className="p-home__pageLink"
          >
            <span className="icon-base icon-size24 icon-sec-oss p-home__pageLinkIcon" />
            <div>
              <Trans
                i18nKey="オープンソース開発者の皆さまへ<break><em>無料提供中！</em></break>"
                components={{
                  break: <span className="p-home__textBreak" />,
                  em: <span className="p-home__textEm" />,
                }}
              />
            </div>
          </a>
        </div>
        <div className="p-home__sliderBox">
          <VerticalSlider
            items={[
              <a
                key="top_devday2025_link"
                id="top_devday2025_link"
                href="https://www.gmo.jp/news/article/9594/"
                target="_blank"
                rel="noopener"
                className="p-home__pageLink"
              >
                <span className="icon-base icon-size24 icon-sec-news p-home__pageLinkIcon" />
                <div className="p-home__textOmit">
                  DevDay2025 Security Night開催！
                </div>
              </a>,
              <a
                key="top_defcon_link"
                id="top_defcon_link"
                href="https://www.gmo.jp/news/article/9592/"
                target="_blank"
                rel="noopener"
                className="p-home__pageLink"
              >
                <span className="icon-base icon-size24 icon-sec-news p-home__pageLinkIcon" />
                <div className="p-home__textOmit">
                  GMOホワイトハッカー、
                  <span className="p-home__textBreak">DEFCON出陣！</span>
                </div>
              </a>,
              // <a
              //   key="top_locked_link"
              //   id="top_locked_link"
              //   href="https://www.gmo.jp/news/article/9600/"
              //   target="_blank"
              //   rel="noopener"
              //   className="p-home__pageLink"
              // >
              //   <span className="icon-base icon-size24 icon-sec-news p-home__pageLinkIcon" />
              //   <div className="p-home__textOmit">
              //     世界最大規模の国際サイバー防衛演習
              //     <span className="p-home__textBreak">に初参加</span>
              //   </div>
              // </a>,
            ]}
            itemHeight={useMediaQuery({ query: '(max-width: 600px)' }) ? 50 : 26}
            visibleCount={1}
          />
        </div>
        <div className="p-home__link">
          <Trans
            i18nKey="※<ag>利用規約</ag>と<pp>プライバシーポリシー</pp>をお読みいただき、同意の上で開始してください。"
            components={{
              ag: (
                <Button
                  as="a"
                  href="https://www.gmo.jp/security/check/agreement/"
                  target="_blank"
                  rel="noopener"
                  variant="text"
                  referrerPolicy="strict-origin-when-cross-origin"
                />
              ),
              pp: (
                <Button
                  as="a"
                  href="https://www.gmo.jp/csr/governance/privacy-policy/"
                  target="_blank"
                  rel="noopener"
                  variant="text"
                  referrerPolicy="strict-origin-when-cross-origin"
                />
              ),
            }}
          />
        </div>
      </div>
    </section>
  );
}

export default Home;
