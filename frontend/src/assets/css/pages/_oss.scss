@charset "utf-8";

@use "../global/variables" as *;
@use "../global/mixin" as *;

/* ossタイトルスタイル mixin */
@mixin oss-title-base(
  $font-size: 36px,
  $width-underline: 72px,
  $height-underline: 8px,
  $color-underline01: $color-oss-blue,
  $color-underline02: $color-oss-green
) {
  position: relative;
  font-size: $font-size;
  font-weight: 600;
  line-height: 1.5;
  color: $color-black;
  margin-bottom: 80px;
  &::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: -32px;
    width: $width-underline;
    height: $height-underline;
    background: linear-gradient(
      90deg,
      $color-underline01 0%,
      $color-underline01 50%,
      $color-underline02 50%,
      $color-underline02 100%
    );
  }
}
/* oss LP */
.p-oss {
  background-color: $color-white;
  &__sec {
    &--gray {
      background-color: $color-gray10;
    }
    &--blue {
      background-color: $color-oss-blue;
    }
  }
  &__inner {
    max-width: 1080px;
    margin: 0 auto;
    padding: 100px 20px;
    &--top {
      padding: 144px 20px 100px;
    }
    &--bottom {
      padding: 100px 20px 0;
    }
  }
  &__title {
    @include oss-title-base;
    &--center {
      text-align: center;
      &::after {
        left: 50%;
        transform: translateX(-50%);
      }
    }
  }
  &__titleImg {
    width: 700px;
    margin: 0 auto;
    padding: 34px 60px;
    background-color: $color-white;
    border-radius: $border-radius-round;
  }
  &__buttonBox {
    display: grid;
    grid-template-columns: repeat(2, 378px);
    column-gap: 20px;
    justify-content: center;
    margin: 60px auto 0;
  }
}
.p-ossMain {
  position: relative;
  display: grid;
  max-width: 1200px;
  margin: 0 auto;
  padding: 146px 20px 100px;
  &__title {
    text-align: center;
    & img {
      width: auto;
      height: 202px;
    }
  }
  &__lead {
    font-size: 20px;
    font-weight: 600;
    text-align: center;
    margin-top: 64px;
  }
  &__leadRed {
    color: $color-oss-red;
  }
  &__search {
    width: 100%;
    max-width: 820px;
    margin: 20px auto 0;
  }
}
.p-ossSearch {
  &__error {
    width: 100%;
    margin: 8px auto 0;
    font-size: 12px;
    color: $color-error;
    padding: 0 32px;
  }
}

.p-ossCardList {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}
.p-ossCard {
  display: flex;
  flex-direction: column;
  row-gap: 10px;
  background-color: $color-white;
  border-radius: $border-radius-card;
  box-shadow: $shadow-15;
  padding: 20px;
  height: 100%;
  &__label {
    width: fit-content;
    font-size: 12px;
    font-weight: 600;
    color: $color-white;
    padding: 2px 8px;
    border-radius: $border-radius-card-half;
    background-color: $color-oss-red;
  }
  &__title {
    font-size: 14px;
    font-weight: 600;
  }
  &__text {
    font-size: 12px;
  }
  &__img {
    margin-top: auto;
    & img {
      width: auto;
      height: 53px;
    }
  }
}
.p-ossColumn {
  display: grid;
  column-gap: 60px;
  grid-template-columns: auto 1fr;
  grid-template-areas:
    "img title"
    "img textBox";
  align-items: start;
  &--even {
    grid-template-columns: 1fr auto;
    grid-template-areas:
      "title img"
      "textBox img";
  }
  &__img {
    grid-area: img;
    width: 420px;
    & img {
      filter: drop-shadow($shadow-15);
    }
  }
  &__title {
    grid-area: title;
    @include oss-title-base;
  }
  &__pcBreak {
    display: block;
  }
  &__textBox {
    grid-area: textBox;
  }
  &__titleS {
    font-size: 20px;
  }
  &__titleRed {
    color: $color-oss-red;
  }
  &__text {
    font-size: 16px;
  }
  &__textEm {
    font-weight: 600;
    color: $color-oss-blue;
  }
  &__textS {
    font-size: 10px;
    vertical-align: super;
  }
  &__note {
    & p {
      font-size: 10px;
      color: $color-gray50;
      word-break: break-all;
    }
  }
}
.p-ossFundColumn {
  display: grid;
  column-gap: 60px;
  grid-template-columns: 1fr 1fr;
  grid-template-areas:
    "title demo"
    "lead demo"
    "detail demo";
  align-items: start;
  &__title {
    grid-area: title;
    @include oss-title-base;
  }
  &__lead {
    grid-area: lead;
    font-size: 20px;
    font-weight: 600;
  }
  &__leadRed {
    color: $color-oss-red;
  }
  &__detail {
    grid-area: detail;
    margin-top: 40px;
  }
  &__demo {
    grid-area: demo;
    position: relative;
  }
  &__demoTitle {
    font-size: 20px;
    font-weight: 600;
    color: $color-gray50;
    text-align: center;
    margin-bottom: 20px;
  }
  &__demoImg {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 100px;
    height: auto;
  }
}
.p-ossFundDetailList {
  display: grid;
  row-gap: 24px;
}
.p-ossFundDetail {
  display: flex;
  align-items: baseline;
  &__check {
    display: inline-block;
    position: relative;
    width: 20px;
    margin-right: 20px;
    flex-shrink: 0;
    &::before {
      content: "";
      display: block;
      height: 3px;
      width: 12px;
      background: $color-oss-green;
      border-radius: 2px;
      transform: rotate(45deg);
      position: absolute;
      left: 0;
      top: 0;
      bottom: -2px;
      margin: auto;
    }
    &::after {
      content: "";
      display: block;
      height: 3px;
      width: 20px;
      background: $color-oss-green;
      border-radius: 2px;
      position: absolute;
      transform: rotate(-45deg);
      left: 6px;
      bottom: 0;
      margin: auto;
    }
  }
}
.p-ossFundDetailImg {
  margin-top: 20px;
  margin-left: 40px;
}
.p-ossReport {
  display: grid;
  column-gap: 60px;
  grid-template-columns: 1fr auto;
  grid-template-areas:
    "title img"
    "textBox img";
  align-items: start;
  &__img {
    grid-area: img;
    width: 420px;
  }
  &__title {
    grid-area: title;
    @include oss-title-base(36px, 72px, 8px, $color-white, $color-white);
    color: $color-white;
  }
  &__titleS {
    display: inline-block;
    font-size: 28px;
    line-height: 1.6;
  }
  &__textBox {
    grid-area: textBox;
  }
  &__titleRed {
    color: $color-oss-red;
  }
  &__text {
    font-size: 16px;
    color: $color-white;
  }
  &__textEm {
    font-weight: 600;
  }
  &__textS {
    font-size: 10px;
    vertical-align: super;
  }
  &__note {
    & p {
      font-size: 10px;
      color: $color-gray50;
    }
  }
  &__button {
    width: 378px;
    margin: 40px auto 0;
  }
}
.p-ossFlow {
  &__title {
    @include oss-title-base;
    &--white {
      @include oss-title-base(36px, 72px, 8px, $color-white, $color-white);
    }
  }
}
.p-ossFlowList {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 60px;
  & li {
    position: relative;
    display: grid;
    &:not(:last-child)::before {
      content: "";
      position: absolute;
      top: 50%;
      right: -30px;
      display: inline-block;
      width: 20px;
      height: 20px;
      border-top: 2px solid $color-gray35;
      border-right: 2px solid $color-gray35;
      transform: rotate(45deg) translateY(-50%);
    }
  }
}
.p-ossFlowCard {
  background-color: $color-white;
  border-radius: $border-radius-card;
  box-shadow: $shadow-15;
  padding: 20px;
  &__order {
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    font-size: 32px;
    font-weight: 600;
    color: $color-green-alpha20;
  }
  &__title {
    position: relative;
    font-size: 20px;
    font-weight: 600;
    text-align: center;
    color: $color-green;
    margin-bottom: 20px;
  }
  &__text {
    font-size: 16px;
  }
}
.p-ossEnd {
  &__title {
    font-size: 36px;
    font-weight: 600;
    line-height: 1.5;
    text-align: center;
    color: $color-black;
  }
  &__titleRed {
    color: $color-oss-red;
  }
  &__pcBreak {
    display: block;
  }
  &__search {
    width: 100%;
    max-width: 820px;
    margin: 60px auto 0;
  }
  &__img {
    position: relative;
    overflow: hidden;
    margin-top: 40px;
    height: 180px;
    & img {
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      height: 266px;
    }
  }
}

/* margin */
.p-ossColumn__text + .p-ossColumn__text,
.p-ossColumn__text + .p-ossColumn__note,
.p-ossReport__text + .p-ossReport__text,
.p-ossReport__text + .p-ossReport__note {
  margin-top: 20px;
}
.p-ossColumn + .p-ossColumn {
  margin-top: 100px;
}
.p-oss__titleImg + .p-ossFundColumn {
  margin-top: 60px;
}
.p-ossFundColumn + .p-ossFlow {
  border-top: 1px solid $color-gray35;
  margin-top: 80px;
  padding-top: 80px;
}

/* sp style */
@include for-device("sp") {
  .p-oss {
    &__inner {
      padding: 60px 16px 60px;
      &--bottom {
        padding: 60px 16px 0;
      }
    }
    &__title {
      @include oss-title-base(26px, 60px, 6px);
      &--center {
        &::after {
          left: 50%;
          transform: translateX(-50%);
        }
      }
    }
    &__titleImg {
      width: 100%;
      text-align: center;
      padding: 20px 16px;
      & img {
        width: auto;
        height: 60px;
      }
    }
    &__buttonBox {
      grid-template-columns: 1fr;
      grid-template-rows: repeat(2, 1fr);
      row-gap: 20px;
    }
  }
  .p-ossMain {
    position: relative;
    padding: 140px 16px 60px;
    &__title {
      & img {
        height: 120px;
      }
    }
    &__lead {
      font-size: 16px;
      margin-top: 48px;
    }
    &__spBreak {
      display: block;
    }
    &__search {
      margin-top: 24px;
    }
  }
  .p-ossSearch {
    &__error {
      padding: 0 24px;
    }
  }
  .p-ossCardList {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
  }
  .p-ossColumn {
    grid-template-columns: inherit;
    grid-template-areas: "title" "img" "textBox";
    gap: 0;
    &--even {
      grid-template-areas: "title" "img" "textBox";
    }
    &__img {
      width: 100%;
      margin-bottom: 40px;
    }
    &__title {
      @include oss-title-base(26px, 60px, 6px);
    }
    &__pcBreak {
      display: inline-block;
    }
    &__titleS {
      font-size: 16px;
    }
  }
  .p-ossFundColumn {
    grid-template-columns: inherit;
    grid-template-areas:
      "title"
      "lead"
      "demo"
      "detail";
    &__title {
      @include oss-title-base(26px, 60px, 6px);
    }
    &__demoTitle {
      font-size: 16px;
    }
    &__demoImg {
      width: 80px;
      height: auto;
    }
  }
  .p-ossFlow {
    &__title {
      @include oss-title-base(26px, 60px, 6px);
    }
  }
  .p-ossFlowList {
    display: flex;
    flex-direction: column;
    & li {
      position: relative;
      display: grid;
      &:not(:last-child)::before {
        display: none;
      }
      &:not(:last-child)::after {
        content: "";
        position: absolute;
        bottom: -40px;
        left: 50%;
        display: inline-block;
        width: 20px;
        height: 20px;
        border-bottom: 2px solid $color-gray35;
        border-right: 2px solid $color-gray35;
        border-radius: 1px;
        transform: rotate(45deg) translateX(-50%) translateY(0%);
      }
    }
  }
  .p-ossReport {
    grid-template-columns: inherit;
    grid-template-areas: "title" "textBox" "img";
    gap: 0;
    &__img {
      width: 100%;
      margin-bottom: 40px;
    }
    &__title {
      @include oss-title-base(26px, 60px, 6px, $color-white, $color-white);
      color: $color-white;
    }
    &__titleS {
      font-size: 18px;
    }
    &__button {
      width: 100%;
    }
  }
  .p-ossEnd {
    &__title {
      font-size: 26px;
    }
    &__titleRed {
      display: block;
    }
    &__pcBreak {
      display: inline-block;
    }
    &__img {
      height: 110px;
      & img {
        height: 160px;
      }
    }
  }

  /* margin */
  .p-ossColumn + .p-ossColumn {
    margin-top: 60px;
  }
  .p-oss__titleImg + .p-ossFundColumn {
    margin-top: 40px;
  }
  .p-ossFundColumn + .p-ossFlow {
    margin-top: 60px;
    padding-top: 60px;
  }
  .p-ossReport__textBox + .p-ossReport__img,
  .p-ossFundColumn__lead + .p-ossFundColumn__demo {
    margin-top: 40px;
  }
}
