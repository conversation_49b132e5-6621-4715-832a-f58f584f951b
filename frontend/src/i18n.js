import i18n from 'i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import Backend from 'i18next-http-backend';
import { initReactI18next } from 'react-i18next';
import { localeUrls } from './locales';

window.i18n = i18n;

i18n
  .use(Backend)
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    fallbackLng: 'ja',
    debug: false,

    interpolation: { escapeValue: false },

    backend: {
      loadPath: (lngs, namespaces) => {
        const lng = lngs[0];
        const ns = namespaces[0];
        return localeUrls[lng] && localeUrls[lng][ns] ? localeUrls[lng][ns] : '';
      },
    },

    detection: {
      order: ['navigator', 'htmlTag'],
      checkWhitelist: true,
    },

    whitelist: ['en', 'ja'],

    ns: [
      Object.keys(localeUrls['ja']),
    ],
    defaultNS: 'home',
  });

window.i18n.changeLanguage('en');

export default i18n;
